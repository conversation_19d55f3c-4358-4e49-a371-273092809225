import { Component, inject, signal, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Subject, takeUntil, switchMap } from 'rxjs';

import { FirebaseService, PigeonWithBasePigeon } from './services/firebase.service';
import { AnalysisPollingService, AnalysisProgress } from './services/analysis-polling.service';
import { FileUploadComponent, UploadResult } from './components/file-upload/file-upload.component';
import { AnalysisProgressComponent } from './components/analysis-progress/analysis-progress.component';
import { PigeonDisplayComponent } from './components/pigeon-display/pigeon-display.component';

type AppState = 'upload' | 'analyzing' | 'complete' | 'error';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatSnackBarModule,
    FileUploadComponent,
    AnalysisProgressComponent,
    PigeonDisplayComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements OnInit, OnDestroy {
  private firebaseService = inject(FirebaseService);
  private analysisPollingService = inject(AnalysisPollingService);
  private snackBar = inject(MatSnackBar);
  private destroy$ = new Subject<void>();

  protected title = 'Pigeon Analyzer';

  // Application state
  currentState = signal<AppState>('upload');
  analysisProgress = signal<AnalysisProgress | null>(null);
  capturedPigeon = signal<PigeonWithBasePigeon | null>(null);
  currentCaptureId = signal<string | null>(null);

  ngOnInit() {
    // Sign in anonymously when the app starts
    this.signInUser();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.analysisPollingService.stopAllPolling();
  }

  private async signInUser() {
    try {
      await this.firebaseService.signInAnonymously();
      console.log('User signed in anonymously');
    } catch (error) {
      console.error('Failed to sign in:', error);
      this.snackBar.open('Failed to initialize app. Please refresh the page.', 'Close', {
        duration: 5000
      });
    }
  }

  onUploadComplete(result: UploadResult) {
    console.log('Upload complete:', result);
    this.currentCaptureId.set(result.captureId);
    this.currentState.set('analyzing');
    this.startAnalysisPolling(result.captureId);
  }

  private startAnalysisPolling(captureId: string) {
    this.analysisPollingService.startPolling(captureId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (progress) => {
          console.log('Analysis progress:', progress);
          this.analysisProgress.set(progress);

          if (progress.status === 'FINISHED') {
            this.loadCompletedPigeon(captureId);
          } else if (progress.status === 'ERROR') {
            this.currentState.set('error');
          }
        },
        error: (error) => {
          console.error('Polling error:', error);
          this.currentState.set('error');
          this.snackBar.open('Failed to track analysis progress', 'Close', {
            duration: 5000
          });
        }
      });
  }

  private loadCompletedPigeon(captureId: string) {
    this.firebaseService.getPigeonByCaptureId(captureId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Pigeon loaded:', result);
          this.capturedPigeon.set(result.pigeon);
          this.currentState.set('complete');
        },
        error: (error) => {
          console.error('Failed to load pigeon:', error);
          this.currentState.set('error');
          this.snackBar.open('Failed to load pigeon data', 'Close', {
            duration: 5000
          });
        }
      });
  }

  startNewAnalysis() {
    // Reset state for new analysis
    this.currentState.set('upload');
    this.analysisProgress.set(null);
    this.capturedPigeon.set(null);
    this.currentCaptureId.set(null);

    // Stop any existing polling
    this.analysisPollingService.stopAllPolling();
  }
}
